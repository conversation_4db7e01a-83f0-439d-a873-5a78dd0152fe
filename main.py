import requests

CLIENT_ID = "cDccUN0OmCfGG0RlP6qxvPACkIK3uDnHXOhGnBv1U6ietWc3"
CLIENT_SECRET = "T529aNzjDszToTpuvOYjdWybG0VSS7YWt00OEXzKjtLNsqUiQahGusigXwqkPzTk"

url = "https://developer.api.autodesk.com/authentication/v2/token"

payload = {
    "client_id": CLIENT_ID,
    "client_secret": CLIENT_SECRET,
    "grant_type": "client_credentials",
    "scope": "data:read data:write data:create"
}

response = requests.post(url, data=payload)

# Ajoute une vérification en cas d’erreur
if response.status_code == 200:
    token = response.json()["access_token"]
    print("✅ Token obtenu :", token)
else:
    print("❌ Erreur :", response.status_code, response.text)
