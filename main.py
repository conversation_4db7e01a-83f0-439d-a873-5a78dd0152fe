import requests

CLIENT_ID = "cDccUN0OmCfGG0RlP6qxvPACkIK3uDnHXOhGnBv1U6ietWc3"
CLIENT_SECRET = "T529aNzjDszToTpuvOYjdWybG0VSS7YWt00OEXzKjtLNsqUiQahGusigXwqkPzTk"

url = "https://developer.api.autodesk.com/authentication/v2/token"

payload = {
    "client_id": CLIENT_ID,
    "client_secret": CLIENT_SECRET,
    "grant_type": "client_credentials",
    "scope": "data:read data:write data:create"
}

response = requests.post(url, data=payload)

# Ajoute une vérification en cas d’erreur
if response.status_code == 200:
    token = response.json()["access_token"]
    print("✅ Token obtenu :", token)
else:
    print("❌ Erreur :", response.status_code, response.text)



import firebase_admin
from firebase_admin import db
import requests
from datetime import datetime, timezone  # ✅ Import ajouté
from urllib.parse import quote  # Pour encoder les URLs

# === CONFIGURATION FIREBASE ===
FIREBASE_CREDENTIALS = "projectpdm-85d5c-firebase-adminsdk-fbsvc-2b24649a30.json"
FIREBASE_DATABASE_URL = "https://projectpdm-85d5c-default-rtdb.europe-west1.firebasedatabase.app/"  # ✅ Espace supprimé

# === CONFIGURATION FORGE ===
FORGE_CLIENT_ID = "cDccUN0OmCfGG0RlP6qxvPACkIK3uDnHXOhGnBv1U6ietWc3"
FORGE_CLIENT_SECRET = "T529aNzjDszToTpuvOYjdWybG0VSS7YWt00OEXzKjtLNsqUiQahGusigXwqkPzTk"
HUB_ID = "urn:adsk.dtt:d0jbL0EjSSCUzGeTovbYOQ"
MODEL_ID = "n8XuJ2BoTJeXKQ0Y9u0a8wAAAABhyE09xplFnrs5njSqOlDQAAAAAA"

# === INITIALISATION FIREBASE ===
cred = firebase_admin.credentials.Certificate(FIREBASE_CREDENTIALS)
firebase_admin.initialize_app(cred, {
    'databaseURL': FIREBASE_DATABASE_URL
})

# === FONCTION: Obtenir token Forge ===
def get_forge_token():
    url = "https://developer.api.autodesk.com/authentication/v2/token"
    payload = {
        "client_id": FORGE_CLIENT_ID,
        "client_secret": FORGE_CLIENT_SECRET,
        "grant_type": "client_credentials",
        "scope": "data:read data:write data:create account:read bucket:read bucket:create"  # ✅ Scopes étendus pour Tandem
    }
    res = requests.post(url, data=payload)

    if res.status_code != 200:
        print("❌ Échec de l'authentification Forge")
        print("Statut :", res.status_code)
        print("Réponse :", res.text)
        raise Exception("Impossible d'obtenir le token")

    return res.json()["access_token"]

# === FONCTION: Convertir timestamp Unix ms -> ISO8601 ===
def to_iso(timestamp_ms):
    try:
        # Vérifier si le timestamp est en millisecondes ou secondes
        if timestamp_ms > 1e12:  # Si > 1e12, c'est probablement en millisecondes
            timestamp_s = timestamp_ms / 1000
        else:  # Sinon, c'est probablement déjà en secondes
            timestamp_s = timestamp_ms

        return datetime.fromtimestamp(timestamp_s, tz=timezone.utc).strftime('%Y-%m-%dT%H:%M:%SZ')
    except Exception:
        # Les timestamps dans Firebase semblent être incorrects, utiliser timestamp actuel
        return datetime.now(tz=timezone.utc).strftime('%Y-%m-%dT%H:%M:%SZ')

# === FONCTION: Récupérer les données de vibration ===
def fetch_vibration_data():
    ref = db.reference('/accel_batch')
    batches = ref.get()
    
    if not batches:
        print("❌ Aucune donnée trouvée dans Firebase")
        return []

    all_readings = []
    for batch in batches.values():
        if "readings" in batch:
            for reading in batch["readings"]:
                all_readings.append({
                    "timestamp": to_iso(reading["timestamp"]),
                    "x": reading["x"],
                    "y": reading["y"],
                    "z": reading["z"]
                })

    print(f"✅ {len(all_readings)} lectures récupérées")
    
    formatted_data = {
        "externalId": MODEL_ID,
        "data": []
    }

    for r in all_readings:
        formatted_data["data"].append({
            "timestamp": r["timestamp"],
            "value": {
                "Vibration_X": r["x"],
                "Vibration_Y": r["y"],
                "Vibration_Z": r["z"]
            }
        })

    return [formatted_data]

# === FONCTION: Envoyer les données à Forge ===
def send_to_forge(data, token):
    # Essayer différents endpoints Tandem Data API
    encoded_hub_id = quote(HUB_ID, safe='')

    # Préparer les headers d'abord
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }

    # Essayons différents endpoints possibles
    endpoints_to_try = [
        f"https://developer.api.autodesk.com/tandem/v1/groups/{encoded_hub_id}",
        f"https://developer.api.autodesk.com/tandem/v1/facilities/{MODEL_ID}/streams",
        f"https://developer.api.autodesk.com/tandem/v1/models/{MODEL_ID}/streams",
        f"https://developer.api.autodesk.com/tandem/v1/twins/{MODEL_ID}/streams"
    ]

    print("🔍 Test de différents endpoints...")
    for test_url in endpoints_to_try:
        test_res = requests.get(test_url, headers=headers)
        print(f"GET {test_url} -> Status: {test_res.status_code}")
        if test_res.status_code == 200:
            print("✅ Succès:", test_res.json())
            break
        elif test_res.status_code != 404:
            print("Response:", test_res.text)

    # Utiliser l'endpoint le plus probable pour les streams
    url = f"https://developer.api.autodesk.com/tandem/v1/facilities/{MODEL_ID}/streams"
    
    print(f"📤 Envoi de {len(data[0]['data'])} points de données...")
    print(f"🔗 URL: {url}")
    print(f"📋 Headers: {headers}")
    print(f"📦 Data sample: {data[0]['data'][:2] if data[0]['data'] else 'No data'}")

    # Test avec la clé API Tandem ajoutée
    print("🔄 Test avec la clé API Tandem configurée...")

    res = requests.post(url, headers=headers, json=data)

    print(f"Status Code: {res.status_code}")
    if res.status_code == 200 or res.status_code == 201:
        print("🎉 SUCCÈS! Données envoyées avec succès à Tandem!")
        print("✅ Stream de données de vibration créé/mis à jour")
        print(f"✅ {len(data[0]['data'])} points de données envoyés")
    elif res.status_code == 404:
        print("❌ Erreur 404: Endpoint non trouvé")
        print("� La clé API est configurée mais l'endpoint pourrait être incorrect")
        print("Response:", res.text)
    elif res.status_code == 401 or res.status_code == 403:
        print("❌ Erreur d'authentification/autorisation")
        print("💡 Vérifier les permissions de la clé API Tandem")
        print("Response:", res.text)
    else:
        print(f"❌ Erreur {res.status_code}:")
        print("Response:", res.text)
        print("Headers:", dict(res.headers))

# === LANCEMENT DU SCRIPT ===
if __name__ == "__main__":
    try:
        token = get_forge_token()
        vibration_data = fetch_vibration_data()
        send_to_forge(vibration_data, token)
    except Exception as e:
        print("❌ Erreur :", str(e))
